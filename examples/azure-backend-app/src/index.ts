import { os } from "@orpc/server";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "@orpc/server/fetch";
import { CORSPlugin } from "@orpc/server/plugins";
import { router } from "./router";

const handler = new RPCHandler(router, {
	plugins: [new CORSPlugin()],
});

const port = process.env.PORT || 3000;

console.log(`🚀 Server running at http://localhost:${port}`);
Bun.serve({
	port,
	async fetch(request: Request) {
		const { matched, response } = await handler.handle(request, {
			prefix: "/rpc",
			context: {}, // Provide initial context if needed
		});

		if (matched) {
			return response;
		}

		return new Response("Not found", { status: 404 });
	},
});
