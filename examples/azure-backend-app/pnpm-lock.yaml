lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@orpc/client':
        specifier: ^1.7.2
        version: 1.7.2
      '@orpc/server':
        specifier: ^1.7.2
        version: 1.7.2
      arktype:
        specifier: ^2.1.20
        version: 2.1.20
    devDependencies:
      '@types/bun':
        specifier: latest
        version: 1.2.18(@types/react@19.1.8)
      typescript:
        specifier: ^5.8.3
        version: 5.8.3

packages:

  '@ark/schema@0.46.0':
    resolution: {integrity: sha512-c2UQdKgP2eqqDArfBqQIJppxJHvNNXuQPeuSPlDML4rjw+f1cu0qAlzOG4b8ujgm9ctIDWwhpyw6gjG5ledIVQ==}

  '@ark/util@0.46.0':
    resolution: {integrity: sha512-JPy/NGWn/lvf1WmGCPw2VGpBg5utZraE84I7wli18EDF3p3zc/e9WolT35tINeZO3l7C77SjqRJeAUoT0CvMRg==}

  '@orpc/client@1.7.2':
    resolution: {integrity: sha512-uup/TKm6SDv6ATCwdiYT62SkzEIs2MDIl+wBb5l0DkgavfjVSDHBu+aJJQAZ0cDr2xxmsdntXbEVHCzlXhxTyA==}

  '@orpc/contract@1.7.2':
    resolution: {integrity: sha512-ekjQhP9X17i6cauVbQ751EWy5Vu8V9cxV9NGjar3OcOEOjnHS2t7DHZhbBABPaybp9QjUYurOevZUBRQ2sY9DQ==}

  '@orpc/server@1.7.2':
    resolution: {integrity: sha512-G9AtnCvcS2iEjpCSvZ77ToooGGM9EBrJ86Cb37eBV5AawYpHqEKl7EuPsaET5bFopggmPbWl3T/Bd2lPpRM+1A==}
    peerDependencies:
      crossws: '>=0.3.4'
      ws: '>=8.18.1'
    peerDependenciesMeta:
      crossws:
        optional: true
      ws:
        optional: true

  '@orpc/shared@1.7.2':
    resolution: {integrity: sha512-sxYaVWOnr4p+XFKMQaBF9SdoqMwugINwBPeoHU9lR//Kqn5ze1Q8bGierbyuFemw/q4OAMNwnqpSt7junYMekg==}

  '@orpc/standard-server-aws-lambda@1.7.2':
    resolution: {integrity: sha512-SA6AyLm/Km9GKX09k/vV9dv4CK52HJCvKi+UgRte27VVk47YsmSqqwzCky5AMFYFOQ6I9M5wBsSC81OqGE4ncg==}

  '@orpc/standard-server-fetch@1.7.2':
    resolution: {integrity: sha512-H/6Lwe7V5hwmFLcThkcZM5DIfYF30ysAAwZxwV7xKq674el6pMTFQCMbP0d/UhDeegs/9omqZigY5+cqht+RuQ==}

  '@orpc/standard-server-node@1.7.2':
    resolution: {integrity: sha512-Je/yhedlCAjzTq5nw/9B1G3GwIT3Tllf7urQKHTi1ZA1tR03+NvU811cLt/DRR3DNLxjrnVsauhXsPVvuAWFHg==}

  '@orpc/standard-server-peer@1.7.2':
    resolution: {integrity: sha512-gIFMIxCHAfRiJblEJmTP3CXKk2KFRyhOjED/QxIU2KhwUl2pAokeDhUmaxXIhpCaTCX4RpmoJJz8jO5t8oAtHA==}

  '@orpc/standard-server@1.7.2':
    resolution: {integrity: sha512-W0KwJdpzyMhhiOKKTxT/CXJylGIKhqzh8B/5dUpAYtireLUw6fQ9IbV5LdPYPL5vlYRv/fcF9E+lSgIJpYGrmg==}

  '@standard-schema/spec@1.0.0':
    resolution: {integrity: sha512-m2bOd0f2RT9k8QJx1JN85cZYyH1RqFBdlwtkSlf4tBDYLCiiZnv1fIIwacK6cqwXavOydf0NPToMQgpKq+dVlA==}

  '@types/bun@1.2.18':
    resolution: {integrity: sha512-Xf6RaWVheyemaThV0kUfaAUvCNokFr+bH8Jxp+tTZfx7dAPA8z9ePnP9S9+Vspzuxxx9JRAXhnyccRj3GyCMdQ==}

  '@types/node@24.0.14':
    resolution: {integrity: sha512-4zXMWD91vBLGRtHK3YbIoFMia+1nqEz72coM42C5ETjnNCa/heoj7NT1G67iAfOqMmcfhuCZ4uNpyz8EjlAejw==}

  '@types/react@19.1.8':
    resolution: {integrity: sha512-AwAfQ2Wa5bCx9WP8nZL2uMZWod7J7/JSplxbTmBQ5ms6QpqNYm672H0Vu9ZVKVngQ+ii4R/byguVEUZQyeg44g==}

  arktype@2.1.20:
    resolution: {integrity: sha512-IZCEEXaJ8g+Ijd59WtSYwtjnqXiwM8sWQ5EjGamcto7+HVN9eK0C4p0zDlCuAwWhpqr6fIBkxPuYDl4/Mcj/+Q==}

  bun-types@1.2.18:
    resolution: {integrity: sha512-04+Eha5NP7Z0A9YgDAzMk5PHR16ZuLVa83b26kH5+cp1qZW4F6FmAURngE7INf4tKOvCE69vYvDEwoNl1tGiWw==}
    peerDependencies:
      '@types/react': ^19

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  openapi-types@12.1.3:
    resolution: {integrity: sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==}

  radash@12.1.1:
    resolution: {integrity: sha512-h36JMxKRqrAxVD8201FrCpyeNuUY9Y5zZwujr20fFO77tpUtGa6EZzfKw/3WaiBX95fq7+MpsuMLNdSnORAwSA==}
    engines: {node: '>=14.18.0'}

  type-fest@4.41.0:
    resolution: {integrity: sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA==}
    engines: {node: '>=16'}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  undici-types@7.8.0:
    resolution: {integrity: sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==}

snapshots:

  '@ark/schema@0.46.0':
    dependencies:
      '@ark/util': 0.46.0

  '@ark/util@0.46.0': {}

  '@orpc/client@1.7.2':
    dependencies:
      '@orpc/shared': 1.7.2
      '@orpc/standard-server': 1.7.2
      '@orpc/standard-server-fetch': 1.7.2
      '@orpc/standard-server-peer': 1.7.2

  '@orpc/contract@1.7.2':
    dependencies:
      '@orpc/client': 1.7.2
      '@orpc/shared': 1.7.2
      '@standard-schema/spec': 1.0.0
      openapi-types: 12.1.3

  '@orpc/server@1.7.2':
    dependencies:
      '@orpc/client': 1.7.2
      '@orpc/contract': 1.7.2
      '@orpc/shared': 1.7.2
      '@orpc/standard-server': 1.7.2
      '@orpc/standard-server-aws-lambda': 1.7.2
      '@orpc/standard-server-fetch': 1.7.2
      '@orpc/standard-server-node': 1.7.2
      '@orpc/standard-server-peer': 1.7.2

  '@orpc/shared@1.7.2':
    dependencies:
      radash: 12.1.1
      type-fest: 4.41.0

  '@orpc/standard-server-aws-lambda@1.7.2':
    dependencies:
      '@orpc/shared': 1.7.2
      '@orpc/standard-server': 1.7.2
      '@orpc/standard-server-fetch': 1.7.2
      '@orpc/standard-server-node': 1.7.2

  '@orpc/standard-server-fetch@1.7.2':
    dependencies:
      '@orpc/shared': 1.7.2
      '@orpc/standard-server': 1.7.2

  '@orpc/standard-server-node@1.7.2':
    dependencies:
      '@orpc/shared': 1.7.2
      '@orpc/standard-server': 1.7.2
      '@orpc/standard-server-fetch': 1.7.2

  '@orpc/standard-server-peer@1.7.2':
    dependencies:
      '@orpc/shared': 1.7.2
      '@orpc/standard-server': 1.7.2

  '@orpc/standard-server@1.7.2':
    dependencies:
      '@orpc/shared': 1.7.2

  '@standard-schema/spec@1.0.0': {}

  '@types/bun@1.2.18(@types/react@19.1.8)':
    dependencies:
      bun-types: 1.2.18(@types/react@19.1.8)
    transitivePeerDependencies:
      - '@types/react'

  '@types/node@24.0.14':
    dependencies:
      undici-types: 7.8.0

  '@types/react@19.1.8':
    dependencies:
      csstype: 3.1.3

  arktype@2.1.20:
    dependencies:
      '@ark/schema': 0.46.0
      '@ark/util': 0.46.0

  bun-types@1.2.18(@types/react@19.1.8):
    dependencies:
      '@types/node': 24.0.14
      '@types/react': 19.1.8

  csstype@3.1.3: {}

  openapi-types@12.1.3: {}

  radash@12.1.1: {}

  type-fest@4.41.0: {}

  typescript@5.8.3: {}

  undici-types@7.8.0: {}
